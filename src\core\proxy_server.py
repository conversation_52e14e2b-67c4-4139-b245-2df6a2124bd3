import sys
import json
import urllib
import asyncio
import aiohttp
import urllib.parse
from aiohttp import web
from typing import Dict, Optional
from multiprocessing import Process, Queue
from core.log_manager import LogManager
from core.storage_manager import StorageManager
from core.config_manager import ConfigManager

logger = LogManager.get_logger(__name__)


class ProxyServer:
    def __init__(self, config_path: str, status_queue: Queue = None):
        self.config_path = config_path
        self.config = ConfigManager(config_path).get_settings().get("proxy", {})
        self.app = web.Application()
        self.status_queue = status_queue
        self.process = None
        self._setup_routes()

    def _setup_routes(self):
        def add_route(method, path, handler):
            self.app.router.add_route(method, path, handler)

        # 处理 STRM 请求
        add_route("*", "/smartstrm/{storage_name}{file_path:/.*}", self._handle_strm)
        # 处理 Emby PlaybackInfo 请求
        add_route(
            "*", "/emby/Items/{id:\d+}/PlaybackInfo", self._handle_emby_playbackinfo
        )
        # 处理 Emby CORS
        add_route("*", "/web/index.html", self._handle_emby_index)
        add_route(
            "*",
            "/web/modules/htmlvideoplayer/basehtmlplayer.js{query:.*}",
            self._handle_emby_basehtmlplayer,
        )
        # 处理 Jellyfin PlaybackInfo 请求
        add_route(
            "*", "/Items/{id:\w+}/PlaybackInfo", self._handle_jellyfin_playbackinfo
        )
        # 智能 302
        add_route("*", "/{emby:(emby/)?}302", self._handle_302)
        # 默认代理转发请求
        add_route("*", "/{path:.*}", self._handle_request)

    async def _handle_strm(self, request: web.Request) -> web.Response:
        """处理STRM请求"""
        match_info = request.match_info
        storage_name = match_info.get("storage_name")
        # 在子进程中初始化 StorageManager
        storage_manager = StorageManager(ConfigManager(self.config_path))
        storage = storage_manager.get_storage(storage_name)
        if not storage:
            return web.Response(status=404, text=f"No storage found: {storage_name}")
        file_path = match_info.get("file_path")
        download_http = storage.get_download_http(file_path)
        url = download_http.get("url")
        headers = download_http.get("headers")
        body = await self._prepare_body(request)
        # 发送请求到目标服务器
        async with aiohttp.ClientSession() as session:
            async with session.request(
                method=request.method,
                url=url,
                headers=headers,
                data=body,
                allow_redirects=False,
            ) as response:
                # 准备响应头
                resp_headers = self._prepare_response_headers(response)
                # 创建流式响应
                stream_response = web.StreamResponse(
                    status=response.status, headers=resp_headers
                )
                await stream_response.prepare(request)
                # 流式传输数据
                async for chunk in response.content.iter_chunked(8192):
                    await stream_response.write(chunk)
                return stream_response

    async def _handle_test(self, request: web.Request) -> web.Response:
        """处理测试请求"""
        return web.Response(status=200, text="test")

    async def _handle_302(self, request: web.Request) -> web.Response:
        """处理302请求"""
        item_id = request.query.get("item_id")
        target_url = urllib.parse.unquote(request.query.get("url"))
        parsed_url = urllib.parse.urlparse(target_url)
        # 智能302
        if parsed_url.path.startswith("/smartstrm/"):
            logger.info(f"Emby item: {item_id} 命中 smartstrm 302")
            target_url = self._smartstrm_to_direct(target_url)
        elif parsed_url.path.startswith("/d/"):
            logger.info(f"Emby item: {item_id} 命中 openlist 302")
            target_url = await self._openlist_to_direct(target_url)
        else:
            logger.info(f"Emby item: {item_id} 命中常规 strm 302")
        if not target_url:
            return web.Response(status=400, text="Missing 'url' parameter")
        raise web.HTTPFound(location=target_url)

    async def _get_response(self, request: web.Request) -> web.Response:
        """魔改替换响应体"""
        try:
            # 获取目标URL
            url = self._get_target_url(request)
            if not url:
                return web.Response(status=404, text="No target URL configured")
            # 准备请求头
            headers = self._prepare_headers(request, self.config.get("headers", {}))
            # 准备请求体
            body = await self._prepare_body(request)
            # 发送请求到目标服务器
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=request.method,
                    url=url,
                    headers=headers,
                    data=body,
                    allow_redirects=False,
                ) as response:
                    # 准备响应头
                    resp_headers = self._prepare_response_headers(response)
                    # 获取原始响应体
                    original_body = await response.content.read()
                    # 创建响应
                    response = web.Response(
                        body=original_body, status=response.status, headers=resp_headers
                    )
                    return response
        except Exception as e:
            logger.error(f"Proxy error: {str(e)}")
            return web.Response(status=500, text=str(e))

    async def _handle_emby_index(self, request: web.Request) -> web.Response:
        """处理 Emby /web/index.html"""
        response = await self._get_response(request)
        response.body = response.body.replace(
            b'<link rel="manifest" href="manifest.json">',
            b'<link rel="manifest" href="manifest.json">\n    <meta name="referrer" content="no-referrer">',
        )
        return response

    async def _handle_emby_basehtmlplayer(self, request: web.Request) -> web.Response:
        """处理 Emby /web/modules/htmlvideoplayer/basehtmlplayer.js"""
        response = await self._get_response(request)
        response.body = response.body.replace(
            b'return mediaSource.IsRemote&&"DirectPlay"===playMethod?null:"anonymous"',
            b"return null",
        )
        return response

    async def _handle_emby_playbackinfo(self, request: web.Request) -> web.Response:
        """处理 Emby PlaybackInfo 请求"""
        match_info = request.match_info
        item_id = match_info.get("id")
        response = await self._get_response(request)
        try:
            data = json.loads(response.body)
            # 判断 http的播放链接 或 /开头的本地文件
            media_path = data["MediaSources"][0]["Path"]
            if media_path.startswith("http"):
                data["MediaSources"][0].update(
                    {
                        # 强制禁止转码
                        "SupportsDirectPlay": True,
                        "SupportsDirectStream": True,
                        "SupportsTranscoding": False,
                        "TranscodingURL": "",
                        "TranscodingSubProtocol": "",
                        "TranscodingContainer": "",
                        # 魔改直链
                        "DirectStreamUrl": f"/302?item_id={item_id}&url={urllib.parse.quote(media_path)}",
                    }
                )
            response.body = json.dumps(data, ensure_ascii=False)
            return response
        except Exception as e:
            logger.error(f"error: {str(e)}")
        return response

    async def _handle_jellyfin_playbackinfo(self, request: web.Request) -> web.Response:
        """处理 Jellyfin PlaybackInfo 请求"""
        match_info = request.match_info
        item_id = match_info.get("id")
        response = await self._get_response(request)
        try:
            data = json.loads(response.body)
            # 判断 http的播放链接 或 /开头的本地文件
            media_path = data["MediaSources"][0]["Path"]
            if media_path.startswith("http"):
                data["MediaSources"][0].update(
                    {
                        # 强制禁止转码
                        "SupportsDirectPlay": True,
                        "SupportsDirectStream": True,
                        "SupportsTranscoding": False,
                        "TranscodingURL": "",
                        "TranscodingSubProtocol": "",
                        "TranscodingContainer": "",
                        # 魔改直链
                        "Path": f"/302?item_id={item_id}&url={urllib.parse.quote(media_path)}",
                    }
                )
            response.body = json.dumps(data, ensure_ascii=False)
            return response
        except Exception as e:
            logger.error(f"error: {str(e)}")
        return response

    async def _handle_request(self, request: web.Request) -> web.Response:
        """默认代理转发请求"""
        try:
            # 检测是否为WebSocket升级请求
            if self._is_websocket_request(request):
                return await self._handle_websocket(request)

            # 获取目标URL
            url = self._get_target_url(request)
            if not url:
                return web.Response(status=404, text="No target URL configured")
            # 准备请求头
            headers = self._prepare_headers(request, self.config.get("headers", {}))
            # 准备请求体
            body = await self._prepare_body(request)
            # 发送请求到目标服务器
            async with aiohttp.ClientSession() as session:
                async with session.request(
                    method=request.method,
                    url=url,
                    headers=headers,
                    data=body,
                    allow_redirects=False,
                ) as response:
                    # 准备响应头
                    resp_headers = self._prepare_response_headers(response)
                    # 创建流式响应
                    stream_response = web.StreamResponse(
                        status=response.status, headers=resp_headers
                    )
                    await stream_response.prepare(request)
                    # 流式传输数据
                    async for chunk in response.content.iter_chunked(8192):
                        await stream_response.write(chunk)
                    return stream_response
        except Exception as e:
            logger.error(f"Proxy error: {str(e)}")
            return web.Response(status=500, text=str(e))

    def _smartstrm_to_direct(self, strm_url: str) -> str:
        parsed_url = urllib.parse.urlparse(strm_url)
        # 解析URL以提取存储名和文件路径
        path_parts = parsed_url.path.split("/")
        # 查找smartstrm在路径中的位置
        try:
            smartstrm_index = path_parts.index("smartstrm")
            # 获取存储名，smartstrm后的部分
            storage_name = path_parts[smartstrm_index + 1]
            # 获取文件路径，存储名后的所有部分
            file_path = "/" + "/".join(path_parts[smartstrm_index + 2 :])
            # 初始化StorageManager并获取存储实例
            storage_manager = StorageManager(ConfigManager(self.config_path))
            storage = storage_manager.get_storage(storage_name)
            if storage:
                if download_url := storage.get_download_url(file_path):
                    return download_url
        except (ValueError, IndexError):
            # 如果解析失败，继续使用原 url
            return strm_url

    async def _openlist_to_direct(self, strm_url: str) -> str:
        try:
            async with aiohttp.ClientSession() as session:
                async with session.head(
                    strm_url, allow_redirects=False, timeout=5
                ) as response:
                    # 检查响应状态码是否是重定向
                    if 300 <= response.status < 400:
                        redirect_url = response.headers.get("Location")
                        if redirect_url:
                            return redirect_url
            return strm_url
        except (ValueError, IndexError):
            # 如果解析失败，继续使用原 url
            return strm_url

    def _get_target_url(self, request: web.Request) -> Optional[str]:
        """获取目标URL"""
        target = self.config.get("target")
        if not target:
            return None
        # 构建完整URL
        url = f"{target.rstrip('/')}{request.path}"
        if query := request.query_string:
            url += f"?{query}"
        return url

    def _prepare_headers(self, request: web.Request, custom_headers: Dict = {}) -> Dict:
        """准备请求头"""
        headers = dict(request.headers)
        # 移除一些不需要转发的头
        headers.pop("Host", None)
        headers.pop("Content-Length", None)
        # 添加自定义头
        headers.update(custom_headers)
        return headers

    async def _prepare_body(self, request: web.Request) -> Optional[bytes]:
        """准备请求体"""
        if request.method in ("GET", "HEAD", "OPTIONS"):
            return None
        return await request.read()

    def _prepare_response_headers(self, response: aiohttp.ClientResponse) -> Dict:
        """准备响应头"""
        headers = dict(response.headers)
        # 移除一些不需要转发的头
        headers.pop("Transfer-Encoding", None)
        headers.pop("Content-Encoding", None)
        headers.pop("Content-Length", None)
        return headers

    def _get_target_title(self):
        """获取目标标题"""
        target = self.config.get("target")
        if not target:
            return None
        try:
            import re
            import requests

            response = requests.get(target, timeout=5)
            if response.status_code == 200:
                if title_match := re.search(
                    r"<title[^>]*>(.*?)</title>",
                    response.text,
                    re.IGNORECASE | re.DOTALL,
                ):
                    return title_match.group(1).strip()
            return None
        except Exception as e:
            return None

    def _is_websocket_request(self, request: web.Request) -> bool:
        """检测是否为WebSocket请求"""
        headers = request.headers
        return (
            request.method == "GET"
            and "upgrade" in headers.get("Connection", "").lower()
            and headers.get("Upgrade", "").lower() == "websocket"
            and headers.get("Sec-WebSocket-Version") == "13"
            and bool(headers.get("Sec-WebSocket-Key"))
        )

    async def _handle_websocket(self, request: web.Request) -> web.Response:
        """处理WebSocket代理转发"""
        try:
            # 获取目标WebSocket URL
            target_url = self._get_target_url(request)
            if not target_url:
                return web.Response(status=404, text="No target URL configured")
            # 将HTTP URL转换为WebSocket URL
            ws_target_url = target_url.replace("http://", "ws://").replace(
                "https://", "wss://"
            )
            logger.debug(f"建立WebSocket代理连接: {request.path} -> {ws_target_url}")
            # 准备客户端WebSocket连接的头部
            client_headers = self._prepare_websocket_headers(request)
            # 创建服务器端WebSocket响应
            ws_server = web.WebSocketResponse()
            await ws_server.prepare(request)
            # 建立到目标服务器的WebSocket连接
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.ws_connect(
                        ws_target_url,
                        headers=client_headers,
                        protocols=(
                            request.headers.get("Sec-WebSocket-Protocol", "").split(",")
                            if request.headers.get("Sec-WebSocket-Protocol")
                            else None
                        ),
                    ) as ws_client:
                        # 创建双向消息转发任务
                        await asyncio.gather(
                            self._forward_websocket_messages(
                                ws_server, ws_client, "client->server"
                            ),
                            self._forward_websocket_messages(
                                ws_client, ws_server, "server->client"
                            ),
                            return_exceptions=True,
                        )
                except Exception as e:
                    logger.error(f"WebSocket客户端连接失败: {e}")
                    if not ws_server.closed:
                        await ws_server.close(
                            code=aiohttp.WSCloseCode.INTERNAL_ERROR,
                            message=f"Target connection failed: {str(e)}".encode(),
                        )
            return ws_server
        except Exception as e:
            logger.error(f"WebSocket代理错误: {e}")
            return web.Response(status=500, text=f"WebSocket proxy error: {str(e)}")

    def _prepare_websocket_headers(self, request: web.Request) -> Dict:
        """准备WebSocket客户端连接的头部"""
        headers = {}
        # 复制相关的头部，但排除一些不需要的
        for name, value in request.headers.items():
            name_lower = name.lower()
            if name_lower not in [
                "host",
                "connection",
                "upgrade",
                "sec-websocket-key",
                "sec-websocket-version",
                "content-length",
                "transfer-encoding",
            ]:
                headers[name] = value
        # 添加自定义头部
        custom_headers = self.config.get("headers", {})
        headers.update(custom_headers)
        return headers

    async def _forward_websocket_messages(self, ws_from, ws_to, direction: str):
        """转发WebSocket消息"""
        try:
            async for msg in ws_from:
                if msg.type == aiohttp.WSMsgType.TEXT:
                    logger.debug(
                        f"WebSocket {direction} 转发文本消息: {len(msg.data)} 字符"
                    )
                    await ws_to.send_str(msg.data)
                elif msg.type == aiohttp.WSMsgType.BINARY:
                    logger.debug(
                        f"WebSocket {direction} 转发二进制消息: {len(msg.data)} 字节"
                    )
                    await ws_to.send_bytes(msg.data)
                elif msg.type == aiohttp.WSMsgType.PING:
                    logger.debug(f"WebSocket {direction} 转发PING")
                    await ws_to.ping(msg.data)
                elif msg.type == aiohttp.WSMsgType.PONG:
                    logger.debug(f"WebSocket {direction} 转发PONG")
                    await ws_to.pong(msg.data)
                elif msg.type == aiohttp.WSMsgType.CLOSE:
                    logger.debug(
                        f"WebSocket {direction} 连接关闭: code={msg.data}, extra={msg.extra}"
                    )
                    if not ws_to.closed:
                        await ws_to.close(
                            code=msg.data,
                            message=msg.extra.encode() if msg.extra else b"",
                        )
                    break
                elif msg.type == aiohttp.WSMsgType.ERROR:
                    logger.error(f"WebSocket {direction} 错误: {ws_from.exception()}")
                    break
        except Exception as e:
            logger.error(f"WebSocket {direction} 消息转发错误: {e}")
            if not ws_to.closed:
                await ws_to.close(code=aiohttp.WSCloseCode.INTERNAL_ERROR)

    def _run_server(self):
        """运行代理服务器的内部方法"""
        try:
            host = self.config.get("host", "0.0.0.0")
            port = self.config.get("port", 8080)
            # 创建事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            # 创建并启动服务器
            runner = web.AppRunner(self.app)
            loop.run_until_complete(runner.setup())
            site = web.TCPSite(runner, host, port)
            loop.run_until_complete(site.start())
            # 通知主进程服务器已启动
            if self.status_queue:
                self.status_queue.put({"status": "started", "host": host, "port": port})
            try:
                # 运行事件循环
                loop.run_forever()
            except KeyboardInterrupt:
                pass
            finally:
                # 清理资源
                loop.run_until_complete(runner.cleanup())
                loop.close()
                # 通知主进程服务器已停止
                if self.status_queue:
                    self.status_queue.put({"status": "stopped"})
        except Exception as e:
            logger.error(f"代理服务器运行错误: {e}")
            if self.status_queue:
                self.status_queue.put({"status": "error", "error": str(e)})
            sys.exit(1)

    def start(self):
        """启动代理服务器"""
        if self.process is not None and self.process.is_alive():
            return False, "代理服务器已经在运行"
        if not self.config.get("target"):
            return False, "请先配置目标地址"
        # 创建状态队列
        if not self.status_queue:
            self.status_queue = Queue()
        # 启动代理服务器进程
        self.process = Process(target=self._run_server, daemon=True)
        self.process.start()
        # 等待启动状态
        try:
            status = self.status_queue.get(timeout=5)
            if status.get("status") == "started":
                self.config["title"] = self._get_target_title()
                message = f"代理服务器已启动: http://{status.get('host')}:{status.get('port')}"
                logger.info(message)
                return True, message
            elif status.get("status") == "error":
                message = f"代理服务器启动失败: {status.get('error')}"
                logger.error(message)
                return False, message
        except:
            return False, "启动超时"

    def stop(self):
        """停止代理服务器"""
        if self.process is None or not self.process.is_alive():
            return False, "代理服务器未运行"
        try:
            # 终止进程
            self.process.terminate()
            self.process.join(timeout=5)
            self.process = None
            # 清理状态队列
            if self.status_queue:
                self.status_queue.close()
                self.status_queue = None
            message = f"代理服务器已停止"
            logger.info(message)
            return True, message
        except Exception as e:
            return False, f"停止失败: {str(e)}"

    def is_running(self):
        """检查代理服务器是否正在运行"""
        return self.process is not None and self.process.is_alive()

    def update_config(self, config: dict):
        """更新代理服务器配置"""
        self.config = config
        if self.is_running():
            self.stop()
        if config.get("enabled", False):
            return self.start()
        return True, "配置已更新"
