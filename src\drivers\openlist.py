import os
import traceback
import requests
from typing import Dict, Any, List, Optional
import datetime
from urllib.parse import urljoin
from ._base import BaseDriver
from core.log_manager import LogManager

logger = LogManager.get_logger(__name__)


class OpenListDriver(BaseDriver):
    """OpenList 驱动类，用于与 OpenList API 交互"""

    DRIVER_TYPE = "openlist"

    DRIVER_CONFIG = {
        "url": {
            "type": "string",
            "required": True,
            "label": "OpenList 地址",
            "placeholder": "http://***********:5244",
            "tip": "",
        },
        "token": {
            "type": "string",
            "required": True,
            "label": "Token",
            "tip": "可在 OpenList 管理后台-设置-其他-令牌 获取",
        },
        "ls_refresh": {
            "type": "boolean",
            "required": False,
            "label": "强制刷新目录",
            "tip": "使 OpenList 发出请求从网盘获取最新列表，有大合集目录时注意风控",
        },
    }

    DRIVER_TIPS = {"type": "info", "message": "使用 OpenList/Alist v3 API 方式访问"}

    def __init__(self, url: str, token: str, ls_refresh: bool = False):
        """初始化 OpenList 驱动

        Args:
            url: OpenList API 地址
            token: OpenList API Token
        """
        self.name = None
        self.url = url.rstrip("/")
        self.token = token
        self.ls_refresh = ls_refresh
        self.headers = {
            "Authorization": token,
            "Content-Type": "application/json;charset=UTF-8",
        }

    def _request(self, method: str, path: str, **kwargs) -> Optional[Dict[str, Any]]:
        """发送请求到 OpenList API

        Args:
            method: 请求方法
            path: 请求路径
            **kwargs: 请求参数

        Returns:
            Optional[Dict[str, Any]]: 响应数据，如果请求失败则返回 None
        """
        try:
            url = urljoin(self.url, path)
            response = requests.request(method, url, headers=self.headers, **kwargs)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"请求 OpenList API 失败: {e}")
            return None

    def _convert_time(self, time_str: str) -> float:
        """将 ISO 8601 格式的日期时间字符串转换为时间戳（秒）

        Args:
            time_str: ISO 8601 格式的日期时间字符串，例如 '2024-01-16T04:05:29.435Z'

        Returns:
            float: 时间戳（秒），如果转换失败则返回 None
        """
        try:
            datetime_object = datetime.datetime.fromisoformat(time_str)
            if datetime_object.tzinfo:
                datetime_utc = datetime_object.astimezone(datetime.timezone.utc)
            else:
                datetime_utc = datetime_object.replace(tzinfo=datetime.timezone.utc)
            timestamp = datetime_utc.timestamp()
            return timestamp
        except ValueError as e:
            logger.error(f"转换日期时间字符串失败: {e}")
            return None

    def list_files(self, path: str = "/") -> List[Dict[str, Any]]:
        """列出指定路径下的文件

        Args:
            path: 要列出的路径

        Returns:
            List[Dict[str, Any]]: 文件列表，每个文件包含 name, isdir, path, size, modified, created, type 等信息
        """
        try:
            data = self._request(
                "POST",
                "/api/fs/list",
                json={"path": path, "refresh": self.ls_refresh},
            )
            if not data or data["code"] != 200:
                logger.error(f"OpenList API 返回错误: {data}")
                return []

            files = []
            for item in data["data"]["content"]:
                files.append(
                    {
                        "name": item["name"],
                        "isdir": item["is_dir"],
                        "path": os.path.join(path, item["name"]).replace("\\", "/"),
                        "size": item["size"] if not item["is_dir"] else "",
                        "modified": self._convert_time(item["modified"]),
                        "created": self._convert_time(item["created"]),
                        "type": item["type"],
                        "sign": item["sign"],
                    }
                )
            return files
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            traceback.print_exc()
            return []

    def delete_file(self, path: str) -> bool:
        """删除指定文件

        Args:
            path: 文件路径

        Returns:
            bool: 是否删除成功
        """
        try:
            dir = os.path.dirname(path)
            name = os.path.basename(path)
            data = self._request(
                "POST", "/api/fs/remove", json={"dir": dir, "names": [name]}
            )
            return data and data["code"] == 200
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            return False

    def rename_file(self, path: str, new_name: str) -> bool:
        """重命名文件或目录

        Args:
            path: 原文件路径
            new_name: 新文件名

        Returns:
            bool: 是否重命名成功
        """
        try:
            dir = os.path.dirname(path)
            old_name = os.path.basename(path)
            data = self._request(
                "POST", "/api/fs/rename", json={"path": path, "name": new_name}
            )
            return data and data["code"] == 200
        except Exception as e:
            logger.error(f"重命名文件失败: {e}")
            return False

    def get_download_url(self, path: str, file_info: Dict[str, Any]) -> str:
        """获取下载 URL

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            str: 下载 URL
        """
        sign_param = "" if not file_info.get("sign") else f"?sign={file_info['sign']}"
        return f"{self.url}/d{path}{sign_param}"

    def get_strm_url(self, path: str, file_info: Dict[str, Any] = {}) -> str:
        """获取 strm URL

        Args:
            path: 文件路径
            file_info: 文件信息
        """
        return self.get_download_url(path, file_info)

    def get_file_data(self, path: str, file_info: Dict[str, Any] = {}) -> bytes:
        """获取文件二进制数据

        Args:
            path: 文件路径
            file_info: 文件信息
        Returns:
            bytes: 文件二进制数据
        """
        try:
            download_url = self.get_download_url(path, file_info)
            response = requests.get(download_url)
            response.raise_for_status()

            # 检查返回的是否是JSON数据且包含错误码
            content_type = response.headers.get("content-type", "")
            if "application/json" in content_type:
                json_data = response.json()
                if json_data.get("code") != 200:
                    raise Exception(f"OpenList API 返回错误: {json_data}")

            return response.content
        except Exception as e:
            logger.error(f"获取文件数据失败: {e}")
            return b""
